#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
实验流程控制模块
整合所有组件，实现完整的眼动实验流程
"""

import time
import random
import json
import os
from datetime import datetime
from typing import Dict, List, Optional

# 导入自定义模块
from experiment_materials import ExperimentMaterials
from eyelink_manager import EyeLinkManager
from experiment_display import ExperimentDisplay
from data_manager import DataManager

class CuriosityExperiment:
    """好奇心瞳孔实验主控制类"""
    
    def __init__(self, participant_id: str, use_eyelink: bool = True, fullscreen: bool = True):
        """
        初始化实验
        
        Args:
            participant_id: 被试ID
            use_eyelink: 是否使用EyeLink
            fullscreen: 是否全屏显示
        """
        self.participant_id = participant_id
        self.use_eyelink = use_eyelink
        self.fullscreen = fullscreen
        self.screen_size = (1920, 1080)
        
        # 实验参数（根据实验流程设置）
        self.timing = {
            'fixation_baseline': 2.0,  # 基线校准时间
            'question_display': 5.0,                   # 问题显示时间
            'answer_input': None,                      # 答案输入时间（无限制，按回车确认）
            'curiosity_rating': 10.0,                  # 好奇心评分时间
            'pupil_baseline': 3.0,                     # 瞳孔基线时间
            'answer_display': 10.0,                    # 答案显示时间（动态控制）
            'pleasure_rating': None,                   # 愉悦度评分时间
            'surprise_rating': None,                   # 意外程度评分时间
            'gaze_duration_threshold': 1.0             # 注视按钮区域的持续时间阈值（秒）
        }
        
        # 评分标签
        self.curiosity_labels = ["完全不想知道", "不太想知道", "知不知道无所谓", "比较感兴趣", "超级好奇"]
        self.pleasure_labels = ["太无聊了", "有点无聊", "一般般", "挺有意思", "太有趣了"]
        self.surprise_labels = ["不意外", "有一点意外", "很意外"]
        
        # 初始化组件
        self.materials = None
        self.eyelink = None
        self.display = None
        self.data_manager = None
        self.experiment_data = []

        # 使用新的数据管理器（自动创建data目录结构）
        self.data_manager = DataManager(participant_id)
        self.data_dir = self.data_manager.data_dir
    
    def initialize_components(self) -> bool:
        """
        初始化所有实验组件
        
        Returns:
            初始化是否成功
        """
        print("初始化实验组件...")
        
        # 初始化材料管理器
        try:
            self.materials = ExperimentMaterials()
            if not self.materials.question_answer_pairs:
                print("错误：无法加载实验材料")
                return False
            print(f"✓ 材料加载成功，共 {len(self.materials.question_answer_pairs)} 道题目")
        except Exception as e:
            print(f"✗ 材料加载失败: {e}")
            return False
        
        # 初始化显示
        try:
            self.display = ExperimentDisplay(fullscreen=self.fullscreen)
            print("✓ 显示组件初始化成功")
        except Exception as e:
            print(f"✗ 显示组件初始化失败: {e}")
            return False

        # 初始化EyeLink（传入display窗口用于校准）
        if self.use_eyelink:
            try:
                display_window = self.display.win if hasattr(self.display, 'win') else None
                self.eyelink = EyeLinkManager(self.participant_id, self.data_dir, self.screen_size, display_window=display_window)
                if not self.eyelink.connect():
                    print("警告：EyeLink连接失败，继续使用虚拟模式")
                else:
                    print("✓ EyeLink连接成功")
                    if not self.eyelink.setup_tracker():
                        print("警告：EyeLink配置失败")
            except Exception as e:
                print(f"✗ EyeLink初始化失败: {e}")
                return False
        
        return True
    
    def run_calibration(self) -> bool:
        """
        运行EyeLink校准

        Returns:
            校准是否成功
        """
        if not self.use_eyelink or not self.eyelink:
            return True

        print("开始EyeLink校准...")
        self.eyelink.log_calibration_start()
        success = self.eyelink.calibrate()
        self.eyelink.log_calibration_end(success)
        return success
    
    def run_single_trial(self, trial_num: int, question_data: Dict) -> Dict:
        """
        运行单个试次
        
        Args:
            trial_num: 试次编号
            question_data: 题目数据
            
        Returns:
            试次结果数据
        """
        print(f"\n开始试次 {trial_num}: {question_data['question'][:30]}...")
        
        trial_data = {
            'trial_num': trial_num,
            'question_id': question_data['id'],
            'question': question_data['question'],
            'answer': question_data['answer'],
            'start_time': datetime.now().isoformat(),
            'participant_response': None,
            'curiosity_rating': None,
            'pleasure_rating': None,
            'surprise_rating': None,
            'timing_data': {}
        }
        
        # 开始眼动记录
        if self.eyelink:
            self.eyelink.start_recording(trial_num)
            self.eyelink.log_trial_start(trial_num, question_data['question'])

        try:
            # 1. 基线校准（十字准星）+ 漂移校正
            print("1. 基线校准和漂移校正...")
            if self.eyelink:
                self.eyelink.log_baseline_start("FIXATION")

            start_time = time.time()
            # 在基线校准时进行漂移校正
            self.display.show_fixation_cross(
                duration=self.timing['fixation_baseline'],
                eyelink_manager=self.eyelink,
                perform_drift_correction=False
            )
            trial_data['timing_data']['baseline_duration'] = time.time() - start_time

            if self.eyelink:
                self.eyelink.log_baseline_end("FIXATION")
            
            # 2. 题目呈现
            print("2. 题目呈现...")
            if self.eyelink:
                self.eyelink.log_question_display(question_data['question'])

            start_time = time.time()
            self.display.show_question(question_data['question'], self.timing['question_display'])
            trial_data['timing_data']['question_duration'] = time.time() - start_time

            if self.eyelink:
                self.eyelink.log_question_end()

            # 3. 答案输入
            print("3. 答案输入...")
            if self.eyelink:
                self.eyelink.log_input_start()

            start_time = time.time()
            participant_response = self.display.get_text_input(
                "请输入您的答案："
            )
            trial_data['timing_data']['input_duration'] = time.time() - start_time
            trial_data['participant_response'] = participant_response

            if self.eyelink:
                self.eyelink.log_input_end(participant_response or "")
            
            # 4. 好奇心评分
            print("4. 好奇心评分...")
            if self.eyelink:
                self.eyelink.log_rating_start("CURIOSITY")

            start_time = time.time()
            curiosity_rating = self.display.get_rating(
                "你有多想继续读下去，请给你的好奇程度打分",
                (1, 5), self.curiosity_labels, self.timing['curiosity_rating']
            )
            trial_data['timing_data']['curiosity_rating_duration'] = time.time() - start_time
            trial_data['curiosity_rating'] = curiosity_rating

            if self.eyelink:
                self.eyelink.log_rating_end("CURIOSITY", curiosity_rating)

            # 5. 瞳孔基线测量
            print("5. 瞳孔基线测量...")
            if self.eyelink:
                self.eyelink.log_baseline_start("PUPIL")

            start_time = time.time()
            self.display.show_fixation_cross(self.timing['pupil_baseline'])
            trial_data['timing_data']['pupil_baseline_duration'] = time.time() - start_time

            if self.eyelink:
                self.eyelink.log_baseline_end("PUPIL")

            # 6. 答案展示（动态时间控制）
            print("6. 答案展示（动态时间控制）...")
            if self.eyelink:
                self.eyelink.log_answer_display(question_data['answer'])

            start_time = time.time()
            # 使用动态时间控制的答案显示
            self.display.show_answer(
                answer=question_data['answer'],
                eyelink_manager=self.eyelink,
                gaze_duration_threshold=self.timing['gaze_duration_threshold']
            )
            trial_data['timing_data']['answer_duration'] = time.time() - start_time

            if self.eyelink:
                self.eyelink.log_answer_end()
            
            # 7. 愉悦度评分
            print("7. 愉悦度评分...")
            if self.eyelink:
                self.eyelink.log_rating_start("PLEASURE")

            start_time = time.time()
            pleasure_rating = self.display.get_rating(
                "你读完这个答案觉得它多有趣，是否让你觉得愉悦，有多强烈的aha，原来是这样的感觉",
                (1, 5), self.pleasure_labels, self.timing['pleasure_rating']
            )
            trial_data['timing_data']['pleasure_rating_duration'] = time.time() - start_time
            trial_data['pleasure_rating'] = pleasure_rating

            if self.eyelink:
                self.eyelink.log_rating_end("PLEASURE", pleasure_rating)

            # 8. 意外程度评分
            print("8. 意外程度评分...")
            if self.eyelink:
                self.eyelink.log_rating_start("SURPRISE")

            start_time = time.time()
            surprise_rating = self.display.get_rating(
                "答案是否让你觉得很意外，完全没想到？",
                (1, 3), self.surprise_labels, self.timing['surprise_rating']
            )
            trial_data['timing_data']['surprise_rating_duration'] = time.time() - start_time
            trial_data['surprise_rating'] = surprise_rating

            if self.eyelink:
                self.eyelink.log_rating_end("SURPRISE", surprise_rating)
        
        except Exception as e:
            print(f"试次 {trial_num} 执行出错: {e}")
            trial_data['error'] = str(e)
        
        finally:
            # 停止眼动记录
            if self.eyelink:
                self.eyelink.log_trial_end(trial_num)
                self.eyelink.stop_recording()
        
        trial_data['end_time'] = datetime.now().isoformat()
        print(f"试次 {trial_num} 完成")

        return trial_data

    def run_experiment(self, num_questions: int = 1) -> bool:
        """
        运行完整实验

        Args:
            num_questions: 题目数量

        Returns:
            实验是否成功完成
        """
        print(f"\n开始好奇心瞳孔实验 - 被试ID: {self.participant_id}")
        print("=" * 60)

        # 初始化组件
        if not self.initialize_components():
            print("实验初始化失败")
            return False

        # 选择题目
        selected_questions = self.materials.get_random_questions(num_questions)
        if not selected_questions:
            print("无法获取实验题目")
            return False

        print(f"已选择 {len(selected_questions)} 道题目")

        # 保存选中的题目
        questions_file = os.path.join(self.data_dir, "selected_questions.json")
        self.materials.save_selected_questions(selected_questions, questions_file)

        # EyeLink校准
        if not self.run_calibration():
            print("EyeLink校准失败")
            return False

        # 记录实验开始
        if self.eyelink:
            self.eyelink.log_experiment_start()

        # 显示实验说明
        self._show_instructions()

        # 运行所有试次
        for i, question_data in enumerate(selected_questions, 1):
            trial_data = self.run_single_trial(i, question_data)
            self.experiment_data.append(trial_data)

            # 保存试次数据
            self._save_trial_data(trial_data)

            # 试次间休息
            if i < len(selected_questions):
                self._inter_trial_break(i, len(selected_questions))

        # 记录实验结束
        if self.eyelink:
            self.eyelink.log_experiment_end()

        # 实验结束
        self._show_end_message()

        # 保存完整数据
        self._save_experiment_data()

        print("\n实验完成！")
        return True

    def _show_instructions(self):
        """显示实验说明"""
        instructions = """
        欢迎参加好奇心瞳孔实验！

        实验流程：
        1. 注视屏幕中央的十字准星
        2. 阅读问题并思考
        3. 输入您的答案
        4. 对好奇心程度评分
        5. 注视屏幕中央的圆点
        6. 阅读正确答案
        7. 对愉悦度评分
        8. 对意外程度评分

        请保持头部稳定，自然地阅读和思考。

        按任意键开始实验...
        """

        if self.display.dummy_mode:
            print("实验说明（模拟模式）:")
            print(instructions)
            time.sleep(2)
        else:
            # 在PsychoPy中显示说明
            pass  # 这里可以添加详细的说明显示代码

    def _inter_trial_break(self, current_trial: int, total_trials: int):
        """试次间休息"""
        break_message = f"试次 {current_trial}/{total_trials} 完成\n\n请休息一下，按任意键继续..."

        if self.display.dummy_mode:
            print(f"试次间休息: {current_trial}/{total_trials}")
            time.sleep(1)
        else:
            # 在PsychoPy中显示休息信息
            pass

    def _show_end_message(self):
        """显示实验结束信息"""
        end_message = "实验已完成！\n\n感谢您的参与！"

        if self.display.dummy_mode:
            print("实验结束信息（模拟模式）")
            print(end_message)
        else:
            # 在PsychoPy中显示结束信息
            pass

    def _save_trial_data(self, trial_data: Dict):
        """保存单个试次数据"""
        trial_file = os.path.join(self.data_dir, f"trial_{trial_data['trial_num']}.json")
        try:
            with open(trial_file, 'w', encoding='utf-8') as f:
                json.dump(trial_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存试次数据失败: {e}")

    def _save_experiment_data(self):
        """保存完整实验数据"""
        experiment_summary = {
            'participant_id': self.participant_id,
            'experiment_start': self.experiment_data[0]['start_time'] if self.experiment_data else None,
            'experiment_end': self.experiment_data[-1]['end_time'] if self.experiment_data else None,
            'total_trials': len(self.experiment_data),
            'timing_settings': self.timing,
            'trials': self.experiment_data
        }

        summary_file = os.path.join(self.data_dir, "experiment_summary.json")
        try:
            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(experiment_summary, f, ensure_ascii=False, indent=2)
            print(f"实验数据已保存到: {self.data_dir}")
        except Exception as e:
            print(f"保存实验数据失败: {e}")

    def cleanup(self):
        """清理资源"""
        if self.eyelink:
            self.eyelink.close()
        if self.display:
            self.display.close()
        print("资源清理完成")

def main():
    """主函数 - 运行实验"""
    print("好奇心瞳孔冷知识实验")
    print("=" * 40)

    # 获取被试信息
    participant_id = input("请输入被试ID: ").strip()
    if not participant_id:
        participant_id = f"test_{datetime.now().strftime('%H%M%S')}"
        print(f"使用默认ID: {participant_id}")

    # 实验设置
    use_eyelink = input("是否使用EyeLink? (y/n, 默认y): ").strip().lower() != 'n'
    fullscreen = input("是否全屏显示? (y/n, 默认y): ").strip().lower() != 'n'

    # 创建实验对象
    experiment = CuriosityExperiment(
        participant_id=participant_id,
        use_eyelink=use_eyelink,
        fullscreen=fullscreen
    )

    try:
        # 运行实验
        success = experiment.run_experiment(num_questions=1)

        if success:
            print("\n✓ 实验成功完成！")
        else:
            print("\n✗ 实验未能完成")

    except KeyboardInterrupt:
        print("\n实验被用户中断")
    except Exception as e:
        print(f"\n实验出现错误: {e}")
    finally:
        # 清理资源
        experiment.cleanup()

if __name__ == "__main__":
    main()
